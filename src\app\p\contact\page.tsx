import { Metadata } from 'next';
import { PageHeader } from '@/components/PageHeader';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Mail, MessageSquare, Clock, HelpCircle, Lightbulb, Bug } from 'lucide-react';

export const metadata: Metadata = {
  title: 'اتصل بنا - جامع الأدوات',
  description: 'تواصل مع فريق جامع الأدوات. نرحب بآرائكم واقتراحاتكم وأسئلتكم لتحسين خدماتنا وإضافة أدوات جديدة.',
  keywords: ['اتصل بنا', 'تواصل', 'دعم فني', 'اقتراحات', 'آراء المستخدمين', 'جامع الأدوات'],
  robots: {
    index: true,
    follow: true,
  },
};

const contactReasons = [
  {
    icon: Lightbulb,
    title: 'اقتراح أداة جديدة',
    description: 'هل لديك فكرة لأداة مفيدة تود إضافتها؟ شاركنا اقتراحك',
    color: 'text-yellow-600 bg-yellow-50'
  },
  {
    icon: Bug,
    title: 'الإبلاغ عن مشكلة',
    description: 'واجهت مشكلة في إحدى الأدوات؟ أخبرنا لنقوم بإصلاحها',
    color: 'text-red-600 bg-red-50'
  },
  {
    icon: HelpCircle,
    title: 'طلب المساعدة',
    description: 'تحتاج مساعدة في استخدام إحدى الأدوات؟ نحن هنا لمساعدتك',
    color: 'text-blue-600 bg-blue-50'
  },
  {
    icon: MessageSquare,
    title: 'تعليق أو اقتراح',
    description: 'شاركنا رأيك في الموقع أو اقترح تحسينات',
    color: 'text-green-600 bg-green-50'
  }
];

const faqs = [
  {
    question: 'هل الأدوات مجانية حقاً؟',
    answer: 'نعم، جميع الأدوات المتوفرة على موقعنا مجانية بالكامل ولا تتطلب أي رسوم أو اشتراك.'
  },
  {
    question: 'هل تحفظون بياناتي الشخصية؟',
    answer: 'لا، نحن لا نحفظ أي بيانات شخصية. جميع العمليات الحسابية تتم محلياً في متصفحك.'
  },
  {
    question: 'كم مرة تضيفون أدوات جديدة؟',
    answer: 'نسعى لإضافة أدوات جديدة بانتظام بناءً على احتياجات المستخدمين واقتراحاتهم.'
  },
  {
    question: 'هل يمكنني استخدام الأدوات على الهاتف؟',
    answer: 'نعم، جميع أدواتنا متوافقة مع الهواتف الذكية والأجهزة اللوحية.'
  }
];

export default function ContactPage() {
  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6 md:p-8">
      <PageHeader 
        title="اتصل بنا" 
        description="نرحب بآرائكم واقتراحاتكم. تواصلوا معنا لأي استفسار أو اقتراح لتحسين خدماتنا"
      />

      {/* Contact Reasons */}
      <div className="mb-12">
        <h2 className="text-2xl font-headline font-bold mb-6 text-center">
          كيف يمكننا مساعدتك؟
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {contactReasons.map((reason, index) => (
            <Card key={index} className="h-full hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-3 mb-2">
                  <div className={`p-3 rounded-lg ${reason.color}`}>
                    <reason.icon className="h-6 w-6" />
                  </div>
                  <CardTitle className="text-lg">{reason.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{reason.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Contact Form */}
      <div className="mb-12">
        <Card>
          <CardHeader>
            <CardTitle className="text-xl flex items-center gap-2">
              <Mail className="h-5 w-5" />
              أرسل لنا رسالة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium mb-2">
                    الاسم
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="اسمك الكريم"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium mb-2">
                    البريد الإلكتروني
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="subject" className="block text-sm font-medium mb-2">
                  الموضوع
                </label>
                <select
                  id="subject"
                  name="subject"
                  className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                >
                  <option value="">اختر الموضوع</option>
                  <option value="suggestion">اقتراح أداة جديدة</option>
                  <option value="bug">الإبلاغ عن مشكلة</option>
                  <option value="help">طلب المساعدة</option>
                  <option value="feedback">تعليق أو اقتراح</option>
                  <option value="other">أخرى</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="message" className="block text-sm font-medium mb-2">
                  الرسالة
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={6}
                  className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                  placeholder="اكتب رسالتك هنا..."
                  required
                ></textarea>
              </div>
              
              <button
                type="submit"
                className="w-full bg-primary text-primary-foreground py-3 px-6 rounded-lg hover:bg-primary/90 transition-colors font-medium"
              >
                إرسال الرسالة
              </button>
            </form>
          </CardContent>
        </Card>
      </div>

      {/* Response Time */}
      <div className="mb-12">
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 mb-3">
              <Clock className="h-5 w-5 text-blue-600" />
              <h3 className="font-bold text-blue-800">وقت الاستجابة</h3>
            </div>
            <p className="text-blue-700">
              نسعى للرد على جميع الرسائل خلال 24-48 ساعة. شكراً لصبركم وتفهمكم.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* FAQ */}
      <div className="mb-12">
        <h2 className="text-2xl font-headline font-bold mb-6 text-center">
          الأسئلة الشائعة
        </h2>
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="text-lg">{faq.question}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{faq.answer}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Alternative Contact */}
      <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-8 text-center">
        <h3 className="text-2xl font-headline font-bold mb-4">
          طرق أخرى للتواصل
        </h3>
        <p className="text-muted-foreground mb-6">
          يمكنكم أيضاً التواصل معنا من خلال وسائل التواصل الاجتماعي أو البريد الإلكتروني المباشر
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="mailto:<EMAIL>"
            className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
          >
            <Mail className="h-4 w-4" />
            <EMAIL>
          </a>
        </div>
      </div>
    </div>
  );
}
