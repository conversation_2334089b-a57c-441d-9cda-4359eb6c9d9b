
'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Download, Eye, Link as LinkIcon, AlertTriangle } from 'lucide-react';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { fetchUrlContent } from '@/lib/actions/fetch-url';
import { Alert, AlertDescription } from '../ui/alert';

const exampleHTML = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            direction: rtl;
            text-align: right;
            line-height: 1.6;
            font-size: 12pt;
            padding: 20px;
        }
        h1 { 
            text-align: center; 
            color: #333; 
            font-size: 24px;
            margin-bottom: 10px;
        }
        hr {
            border: 0;
            height: 1px;
            background: #667eea;
            margin-bottom: 25px;
        }
        p {
            margin-bottom: 15px;
            color: #555;
        }
        .highlight { 
            background-color: #fef9c3; 
            padding: 15px; 
            border-right: 4px solid #facc15; 
            margin: 20px 0;
            border-radius: 5px;
        }
        h3 {
            margin-top: 25px;
            margin-bottom: 10px;
            font-size: 18px;
        }
        ul {
            padding-right: 20px;
            list-style-type: '✓ ';
        }
        li {
            padding-right: 5px;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <h1>مثال على تحويل HTML إلى PDF</h1>
    <hr>
    <p>مرحباً بك!</p>
    <p>هذا مثال على محتوى HTML الذي سيتم تحويله إلى PDF. يمكنك تخصيص هذا المحتوى كما تشاء.</p>
    <div class="highlight">
        <strong>ملاحظة:</strong> يدعم المحول معظم عناصر HTML و CSS للحصول على نتائج احترافية.
    </div>
    <h3>المميزات:</h3>
    <ul>
        <li>دعم HTML5 و CSS3</li>
        <li>معاينة فورية للنتائج</li>
        <li>خيارات متعددة لحجم واتجاه الصفحة</li>
    </ul>
</body>
</html>`;

export function HtmlToPdfTool() {
  const [htmlInput, setHtmlInput] = useState(exampleHTML);
  const [urlInput, setUrlInput] = useState('');
  const [fileName, setFileName] = useState('document');
  const [pageSize, setPageSize] = useState('a4');
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');
  const [scale, setScale] = useState(2);
  const [isLoading, setIsLoading] = useState(false);
  const [fetcherError, setFetcherError] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(true);
  const { toast } = useToast();
  const previewRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (showPreview && previewRef.current) {
      previewRef.current.srcdoc = htmlInput;
    }
  }, [htmlInput, showPreview]);
  
  const handlePreviewClick = () => {
    setShowPreview(true);
  }

  const handleFetchUrl = async () => {
    if (!urlInput) {
      toast({ title: "الرجاء إدخال رابط.", variant: "destructive" });
      return;
    }
    setIsLoading(true);
    setFetcherError(null);
    try {
      const result = await fetchUrlContent(urlInput);
      if (result.success && result.html) {
        setHtmlInput(result.html);
        handlePreviewClick(); 
        toast({ title: "تم جلب المحتوى بنجاح!", description: "تم وضع محتوى الرابط في المحرر." });
      } else {
        setFetcherError(result.error || 'حدث خطأ غير معروف.');
      }
    } catch (error) {
      setFetcherError('فشل الاتصال بالخادم. قد يكون الموقع محمياً أو غير متوفر حالياً.');
    } finally {
      setIsLoading(false);
    }
  };

  const generatePDF = async () => {
    setIsLoading(true);
    toast({ title: "جاري إنشاء PDF...", description: "قد تستغرق هذه العملية بضع ثوانٍ." });

    try {
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.background = 'white';
      
      const pageDimensions = {
        a4: { width: 210, height: 297 },
        letter: { width: 215.9, height: 279.4 },
        legal: { width: 215.9, height: 355.6 },
        a3: { width: 297, height: 420 },
      };
      
      const selectedPage = pageDimensions[pageSize as keyof typeof pageDimensions];
      const pageW = orientation === 'portrait' ? selectedPage.width : selectedPage.height;
      
      tempDiv.style.width = `${pageW}mm`;
      tempDiv.style.padding = `15mm`; 
      tempDiv.style.boxSizing = 'border-box';
      tempDiv.innerHTML = htmlInput;
      
      document.body.appendChild(tempDiv);
      
      const canvas = await html2canvas(tempDiv, {
        scale: scale,
        useCORS: true,
        allowTaint: true,
        logging: false,
        width: tempDiv.scrollWidth,
        height: tempDiv.scrollHeight,
        windowWidth: tempDiv.scrollWidth,
        windowHeight: tempDiv.scrollHeight
      });
      
      document.body.removeChild(tempDiv);

      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: orientation,
        unit: 'mm',
        format: pageSize,
      });

      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      
      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;
      const ratio = canvasHeight / canvasWidth;
      
      const imgWidthInPdf = pdfWidth;
      const imgHeightInPdf = imgWidthInPdf * ratio;

      let position = 0;
      let heightLeft = imgHeightInPdf;

      pdf.addImage(imgData, 'PNG', 0, position, imgWidthInPdf, imgHeightInPdf, undefined, 'FAST');
      heightLeft -= pdfHeight;

      while (heightLeft > 0) {
        position -= pdfHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidthInPdf, imgHeightInPdf, undefined, 'FAST');
        heightLeft -= pdfHeight;
      }
      
      pdf.save(`${fileName}.pdf`);

      toast({
        title: 'نجاح!',
        description: 'تم إنشاء ملف PDF بنجاح وجاري تحميله.',
      });

    } catch (error) {
      console.error("Error generating PDF:", error);
      toast({
        variant: 'destructive',
        title: 'حدث خطأ',
        description: 'فشل في إنشاء ملف PDF. يرجى المحاولة مرة أخرى.',
      });
    } finally {
      setIsLoading(false);
    }
  };


  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
            <CardTitle>جلب المحتوى من رابط</CardTitle>
            <CardDescription>أدخل رابط صفحة ويب لجلب محتوى HTML الخاص بها تلقائيًا.</CardDescription>
        </CardHeader>
        <CardContent>
            <div className="flex flex-col sm:flex-row gap-2">
                <Input
                    type="url"
                    value={urlInput}
                    onChange={(e) => setUrlInput(e.target.value)}
                    placeholder="https://example.com"
                    className="flex-grow"
                    dir="ltr"
                />
                <Button onClick={handleFetchUrl} disabled={isLoading} className="w-full sm:w-auto">
                    {isLoading ? <Loader2 className="ml-2 h-4 w-4 animate-spin" /> : <LinkIcon className="ml-2 h-4 w-4" />}
                    {isLoading ? 'جاري الجلب...' : 'جلب المحتوى'}
                </Button>
            </div>
            {fetcherError && (
                 <Alert variant="destructive" className="mt-4">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>{fetcherError}</AlertDescription>
                </Alert>
            )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card>
            <CardHeader>
            <div className="flex justify-between items-center">
                <CardTitle>محرر HTML</CardTitle>
                <Button variant="outline" size="sm" onClick={() => { setHtmlInput(exampleHTML); }}>مثال</Button>
            </div>
            </CardHeader>
            <CardContent>
            <Textarea
                id="htmlInput"
                value={htmlInput}
                onChange={(e) => setHtmlInput(e.target.value)}
                placeholder="اكتب أو الصق كود HTML هنا..."
                className="min-h-[400px] font-mono text-sm"
                dir="ltr"
            />
            </CardContent>
        </Card>
        
        {showPreview ? (
            <Card>
              <CardHeader>
                <CardTitle>معاينة</CardTitle>
              </CardHeader>
              <CardContent>
                <iframe
                  ref={previewRef}
                  title="Preview"
                  className="w-full h-[400px] border rounded-md bg-white"
                  sandbox="allow-same-origin"
                />
              </CardContent>
            </Card>
        ) : (
             <div className="flex items-center justify-center bg-muted/50 border-2 border-dashed rounded-lg min-h-[400px]">
                <div className="text-center text-muted-foreground">
                    <p>المعاينة ستظهر هنا.</p>
                    <p>اضغط على زر "معاينة" لتحديث العرض.</p>
                </div>
            </div>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>خيارات PDF</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="pageSize">حجم الصفحة</Label>
              <Select value={pageSize} onValueChange={setPageSize}>
                <SelectTrigger><SelectValue /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="a4">A4</SelectItem>
                  <SelectItem value="letter">Letter</SelectItem>
                  <SelectItem value="legal">Legal</SelectItem>
                  <SelectItem value="a3">A3</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="orientation">اتجاه الصفحة</Label>
              <Select value={orientation} onValueChange={(value) => setOrientation(value as 'portrait' | 'landscape')}>
                <SelectTrigger><SelectValue /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="portrait">عمودي</SelectItem>
                  <SelectItem value="landscape">أفقي</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="fileName">اسم الملف</Label>
              <Input id="fileName" value={fileName} onChange={(e) => setFileName(e.target.value)} />
            </div>
             <div className="space-y-2">
              <Label htmlFor="scale">جودة الصورة</Label>
              <Select value={String(scale)} onValueChange={(val) => setScale(Number(val))}>
                <SelectTrigger><SelectValue /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">عادية (أسرع)</SelectItem>
                  <SelectItem value="2">عالية</SelectItem>
                  <SelectItem value="3">عالية جداً</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <div className="flex justify-center gap-4">
        <Button onClick={handlePreviewClick} size="lg" variant="default">
          <Eye className="h-5 w-5 ml-2" />
          معاينة
        </Button>
        <Button onClick={generatePDF} disabled={isLoading} size="lg" variant="default">
          {isLoading ? <Loader2 className="h-5 w-5 ml-2 animate-spin" /> : <Download className="h-5 w-5 ml-2" />}
          {isLoading ? 'جاري إنشاء PDF...' : 'تحميل PDF'}
        </Button>
      </div>
    </div>
  );
}
