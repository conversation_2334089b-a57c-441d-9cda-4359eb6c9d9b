

'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { ExternalLink } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ARAB_COUNTRIES } from '@/lib/constants/currencies';
import type { getIpInfo } from '@/lib/actions/ip';

type IpInfo = Awaited<ReturnType<typeof getIpInfo>>;

interface WhatsappToolsToolProps {
  initialData?: IpInfo;
}

const FormSchema = z.object({
  countryCode: z.string({ required_error: 'الرجاء اختيار رمز الدولة.' }),
  phone: z.string().min(7, 'رقم الهاتف قصير جدًا.').regex(/^\d+$/, 'رقم الهاتف يجب أن يحتوي على أرقام فقط.'),
  message: z.string().optional(),
});

export function WhatsappToolsTool({ initialData }: WhatsappToolsToolProps) {
  const defaultCountryCode = ARAB_COUNTRIES.some(c => c.code === initialData?.phoneCode) ? initialData?.phoneCode : '966';

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      countryCode: defaultCountryCode,
      message: '',
    },
  });

  const onSubmit = (data: z.infer<typeof FormSchema>) => {
    const phoneNumber = data.phone.replace(/\D/g, ''); // Remove non-digits just in case
    const fullPhoneNumber = data.countryCode + phoneNumber;
    const url = `https://wa.me/${fullPhoneNumber}?text=${encodeURIComponent(data.message || '')}`;
    window.open(url, '_blank');
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>انشاء رابط WhatsApp</CardTitle>
        <CardDescription>أرسل رسالة عبر WhatsApp دون الحاجة لحفظ الرقم في جهات الاتصال.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>رقم الهاتف (بدون رمز الدولة)</FormLabel>
                    <FormControl>
                      <Input dir="ltr" placeholder="501234567" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="countryCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>رمز الدولة</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر رمزًا" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {ARAB_COUNTRIES.map((country) => (
                          <SelectItem key={country.code} value={country.code}>
                            <div className="flex items-center gap-2" dir="ltr">
                                <span>{country.flag}</span>
                                <span>+{country.code}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الرسالة (اختياري)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="اكتب رسالتك هنا..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full">
              <ExternalLink className="ml-2 h-4 w-4" />
              فتح المحادثة في WhatsApp
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
