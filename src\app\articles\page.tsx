import { Metadata } from 'next';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PageHeader } from '@/components/PageHeader';
import { Calendar, Clock, ArrowLeft } from 'lucide-react';

export const metadata: Metadata = {
  title: 'مقالات ونصائح مفيدة',
  description: 'مجموعة من المقالات والنصائح المفيدة حول استخدام الأدوات المختلفة والحاسبات والمحولات',
  keywords: ['مقالات عربية', 'نصائح', 'شروحات', 'دليل استخدام', 'حاسبات', 'محولات'],
};

const articles = [
  {
    id: 'how-to-calculate-zakat',
    title: 'كيفية حساب الزكاة بطريقة صحيحة',
    description: 'دليل شامل لحساب زكاة المال والذهب والفضة وفقاً للأحكام الشرعية',
    date: '2024-12-15',
    readTime: '5 دقائق',
    category: 'إسلامية',
  },
  {
    id: 'hijri-calendar-guide',
    title: 'دليل شامل للتقويم الهجري والميلادي',
    description: 'تعرف على الفروق بين التقويمين وكيفية التحويل بينهما بدقة',
    date: '2024-12-10',
    readTime: '7 دقائق',
    category: 'تعليمية',
  },
  {
    id: 'text-tools-productivity',
    title: 'كيف تستخدم أدوات النصوص لزيادة الإنتاجية',
    description: 'نصائح وحيل لاستخدام أدوات النصوص المختلفة لتحسين كتابتك وإنتاجيتك',
    date: '2024-12-05',
    readTime: '6 دقائق',
    category: 'إنتاجية',
  },
  {
    id: 'currency-converter-tips',
    title: 'نصائح لاستخدام محول العملات بفعالية',
    description: 'كيفية الحصول على أدق أسعار الصرف ونصائح للتعامل مع تقلبات العملات',
    date: '2024-11-30',
    readTime: '4 دقائق',
    category: 'مالية',
  },
  {
    id: 'age-calculator-uses',
    title: 'استخدامات مبتكرة لحاسبة العمر',
    description: 'اكتشف الاستخدامات المختلفة لحاسبة العمر في التخطيط والمناسبات',
    date: '2024-11-25',
    readTime: '3 دقائق',
    category: 'عامة',
  },
  {
    id: 'financial-planning-tools',
    title: 'أدوات التخطيط المالي الأساسية',
    description: 'تعرف على الأدوات المالية الأساسية التي تحتاجها لتخطيط مستقبلك المالي',
    date: '2024-11-20',
    readTime: '8 دقائق',
    category: 'مالية',
  },
  {
    id: 'qr-code-complete-guide',
    title: 'الدليل الشامل لرموز QR وكيفية إنشائها',
    description: 'تعلم كل شيء عن رموز QR وكيفية إنشائها واستخدامها في الأعمال والحياة اليومية',
    date: '2024-12-20',
    readTime: '8 دقائق',
    category: 'تقنية',
  },
  {
    id: 'bmi-health-guide',
    title: 'فهم مؤشر كتلة الجسم وأهميته للصحة',
    description: 'دليل شامل لفهم مؤشر كتلة الجسم وكيفية حسابه وتفسير النتائج للحفاظ على صحة أفضل',
    date: '2024-12-18',
    readTime: '6 دقائق',
    category: 'صحة',
  },
  {
    id: 'percentage-calculations-guide',
    title: 'إتقان حسابات النسب المئوية في الحياة العملية',
    description: 'تعلم كيفية حساب النسب المئوية بطرق مختلفة وتطبيقها في المجالات المالية والتجارية',
    date: '2024-12-16',
    readTime: '5 دقائق',
    category: 'تعليمية',
  },
  {
    id: 'unit-conversion-mastery',
    title: 'إتقان تحويل الوحدات: من المتري إلى الإمبراطوري',
    description: 'دليل شامل لتحويل الوحدات المختلفة مع أمثلة عملية ونصائح للحفظ والتذكر',
    date: '2024-12-14',
    readTime: '7 دقائق',
    category: 'تعليمية',
  },
  {
    id: 'whatsapp-business-tools',
    title: 'استخدام أدوات واتساب لتطوير الأعمال',
    description: 'كيفية استخدام أدوات واتساب المختلفة لتحسين التواصل مع العملاء وزيادة المبيعات',
    date: '2024-12-12',
    readTime: '6 دقائق',
    category: 'أعمال',
  },
  {
    id: 'gpa-calculation-systems',
    title: 'أنظمة حساب المعدل التراكمي حول العالم',
    description: 'تعرف على الأنظمة المختلفة لحساب المعدل التراكمي وكيفية التحويل بينها',
    date: '2024-12-08',
    readTime: '5 دقائق',
    category: 'تعليمية',
  },
  {
    id: 'investment-basics-guide',
    title: 'أساسيات الاستثمار للمبتدئين',
    description: 'دليل شامل للمبتدئين في عالم الاستثمار مع شرح المفاهيم الأساسية وحساب العوائد',
    date: '2024-12-06',
    readTime: '10 دقائق',
    category: 'مالية',
  },
  {
    id: 'retirement-planning-guide',
    title: 'التخطيط للتقاعد: ابدأ مبكراً لمستقبل مالي آمن',
    description: 'كيفية التخطيط للتقاعد وحساب المبلغ المطلوب للعيش بكرامة بعد التقاعد',
    date: '2024-12-04',
    readTime: '9 دقائق',
    category: 'مالية',
  },
  {
    id: 'vat-tax-guide',
    title: 'فهم ضريبة القيمة المضافة وكيفية حسابها',
    description: 'دليل شامل لفهم ضريبة القيمة المضافة وكيفية حسابها في المعاملات التجارية',
    date: '2024-12-02',
    readTime: '6 دقائق',
    category: 'مالية',
  },
  {
    id: 'discount-calculation-strategies',
    title: 'استراتيجيات حساب الخصومات والعروض',
    description: 'تعلم كيفية حساب الخصومات المختلفة وتقييم العروض التجارية بذكاء',
    date: '2024-11-28',
    readTime: '4 دقائق',
    category: 'تجارة',
  },
];

const categories = ['الكل', 'إسلامية', 'تعليمية', 'إنتاجية', 'مالية', 'عامة', 'تقنية', 'صحة', 'أعمال', 'تجارة'];

export default function ArticlesPage() {
  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6 md:p-8">
      <PageHeader 
        title="مقالات ونصائح مفيدة" 
        description="مجموعة من المقالات والنصائح المفيدة حول استخدام الأدوات المختلفة والحاسبات والمحولات"
      />
      
      {/* Categories Filter */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category}
              className="px-4 py-2 rounded-full border border-border hover:bg-primary hover:text-primary-foreground transition-colors"
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* Articles Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {articles.map((article) => (
          <Link
            key={article.id}
            href={`/articles/${article.id}`}
            className="group"
          >
            <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary">
              <CardHeader>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                    {article.category}
                  </span>
                  <ArrowLeft className="h-4 w-4 text-muted-foreground transition-transform group-hover:translate-x-[-4px] group-hover:text-primary" />
                </div>
                <CardTitle className="font-headline text-lg leading-tight">
                  {article.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                  {article.description}
                </p>
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>{new Date(article.date).toLocaleDateString('ar-SA-u-nu-latn')}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{article.readTime}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>

      {/* Call to Action */}
      <div className="mt-12 text-center">
        <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg p-8">
          <h3 className="text-2xl font-headline font-bold mb-4">
            هل لديك اقتراح لمقال جديد؟
          </h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            نحن نرحب بأفكاركم واقتراحاتكم لمقالات جديدة تفيد المجتمع العربي. شاركونا أفكاركم!
          </p>
          <Link
            href="/p/contact"
            className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
          >
            اقترح مقالاً
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </div>
      </div>
    </div>
  );
}
