'use server';

interface FetchResult {
  success: boolean;
  html?: string;
  error?: string;
}

export async function fetchUrlContent(url: string): Promise<FetchResult> {
  try {
    // Ensure the URL has a protocol
    let fullUrl = url;
    if (!/^https?:\/\//i.test(url)) {
      fullUrl = `https://${url}`;
    }

    const response = await fetch(fullUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
      },
      // Redirects are followed by default
    });

    if (!response.ok) {
      throw new Error(`فشل في جلب المحتوى. حالة الخادم: ${response.status}`);
    }

    const html = await response.text();
    
    // Add base tag to handle relative URLs for images/css
    const baseTag = `<base href="${new URL(fullUrl).origin}" />`;
    const updatedHtml = html.replace(/(<head[^>]*>)/, `$1${baseTag}`);

    return { success: true, html: updatedHtml };
  } catch (error) {
    console.error('Fetch URL Error:', error);
    if (error instanceof TypeError && error.message.includes('fetch failed')) {
         return { success: false, error: 'فشل الاتصال بالخادم. قد يكون الموقع محمياً أو غير متوفر حالياً. جرب نسخ الكود يدوياً.' };
    }
    return { success: false, error: error instanceof Error ? error.message : 'حدث خطأ غير معروف.' };
  }
}
