
'use client';

import { notFound, useParams } from 'next/navigation';
import Link from 'next/link';
import { toolCategories, Tool } from '@/lib/tools';
import { PageHeader } from '@/components/PageHeader';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { loadToolContent } from '@/lib/content/loader';
import { JsonLd } from '@/components/JsonLd';
import { getToolComponent } from '@/lib/tool-registry';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';

const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;

// This is now a client component to handle dynamic component loading
export default function ToolPage() {
    const params = useParams();
    const slug = Array.isArray(params.slug) ? params.slug[0] : params.slug;

    const [toolData, setToolData] = useState<{ tool: Tool; content: any; initialData: any } | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (!slug) return;

        const allTools = toolCategories.flatMap(c => c.tools);
        const tool = allTools.find(t => t.slug === slug);

        if (!tool) {
            notFound();
            return;
        }

        const fetchData = async () => {
            try {
                const content = await loadToolContent(slug);
                const initialData = tool.getData ? await tool.getData() : {};
                setToolData({ tool, content, initialData });
            } catch (error) {
                console.error("Error loading tool data", error);
                notFound();
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [slug]);

    if (loading || !toolData) {
        // You can add a loading skeleton here
        return <div>جاري تحميل الأداة...</div>;
    }

    const { tool, content, initialData } = toolData;
    const allTools = toolCategories.flatMap(c => c.tools);
    const category = toolCategories.find(cat => cat.tools.some(t => t.slug === slug));

    // --- Related Tools Logic ---
    let relatedTools: Tool[] = [];
    const relatedSlugs = new Set<string>();

    if (content.relatedSlugs) {
        content.relatedSlugs.forEach((relatedSlug: string) => {
            const relatedTool = allTools.find(t => t.slug === relatedSlug);
            if (relatedTool && !relatedSlugs.has(relatedSlug)) {
                relatedTools.push(relatedTool);
                relatedSlugs.add(relatedSlug);
            }
        });
    }

    if (category && relatedTools.length < 6) {
        const categoryTools = category.tools.filter(t => t.slug !== slug && !relatedSlugs.has(t.slug));
        const remainingTools = categoryTools.slice(0, 6 - relatedTools.length);
        relatedTools.push(...remainingTools);
    }

    const ToolComponent = getToolComponent(slug || '');
    if (!ToolComponent) {
        notFound();
        return null;
    }

    const breadcrumbJsonLd: any = siteUrl && category ? {
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: [
            { '@type': 'ListItem', position: 1, name: 'الرئيسية', item: siteUrl },
            { '@type': 'ListItem', position: 2, name: category.name, item: `${siteUrl}/categories/${category.slug}` },
            { '@type': 'ListItem', position: 3, name: tool.name, item: `${siteUrl}/tools/${slug}` },
        ],
    } : null;

    const faqJsonLd: any = content.faq && content.faq.length > 0 ? {
        '@context': 'https://schema.org',
        '@type': 'FAQPage',
        mainEntity: content.faq.map((item: any) => ({
            '@type': 'Question',
            name: item.question,
            acceptedAnswer: { '@type': 'Answer', text: item.answer },
        })),
    } : null;

    const toolJsonLd: any = siteUrl ? {
        '@context': 'https://schema.org',
        '@type': 'WebApplication',
        name: tool.name,
        description: tool.description,
        url: `${siteUrl}/tools/${slug}`,
        applicationCategory: 'UtilityApplication',
        operatingSystem: 'Web Browser',
        offers: { '@type': 'Offer', price: '0', priceCurrency: 'USD', availability: 'https://schema.org/InStock' },
        provider: { '@type': 'Organization', name: 'أدوات بالعربي', url: siteUrl },
        inLanguage: 'ar',
        isAccessibleForFree: true,
        keywords: [tool.name, category?.name, 'أدوات عربية', 'حاسبة مجانية'].filter(Boolean).join(', '),
        dateModified: new Date().toISOString(),
        datePublished: new Date().toISOString()
    } : null;

    const isWideTool = [
        'summarize-arabic-text', 'paraphrase-text', 'resignation-letter-generator', 'financial-aid-request-generator', 'ovulation-calculator', 'zatca-invoice-generator', 'image-editor', 'html-to-pdf', 'text-to-pdf', 'pdf-to-images', 'images-to-pdf', 'pdf-merger', 'split-pdf', 'pdf-compressor', 'image-compressor', 'jpg-to-png-converter', 'png-to-jpg-converter', 'image-to-webp-converter', 'webp-to-png-converter'
    ].includes(slug || '');

    return (
        <div className="w-full flex flex-col items-center">
            {breadcrumbJsonLd && <JsonLd data={breadcrumbJsonLd} />}
            {faqJsonLd && <JsonLd data={faqJsonLd} />}
            {toolJsonLd && <JsonLd data={toolJsonLd} />}
            
            <div className={cn("w-full flex justify-center", isWideTool ? "max-w-6xl" : "max-w-4xl")}>
                <div className={cn("w-full", isWideTool ? "max-w-full" : "max-w-2xl")}>
                    <ToolComponent initialData={initialData} />
                </div>
            </div>
            
            <div className="w-full max-w-4xl mt-16 space-y-12">
                {relatedTools.length > 0 && (
                    <section>
                        <h2 className="text-2xl font-headline font-bold text-center mb-6">أدوات ذات صلة</h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                            {relatedTools.map(relatedTool => (
                                <Link key={relatedTool.path} href={relatedTool.path} className="group">
                                    <Card className="h-full transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-primary">
                                        <CardHeader>
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-4">
                                                    {relatedTool.icon && (
                                                        <div className="p-2 rounded-full bg-primary/10 text-primary">
                                                            <relatedTool.icon className="w-5 h-5" />
                                                        </div>
                                                    )}
                                                    <CardTitle className="font-headline text-lg">{relatedTool.name}</CardTitle>
                                                </div>
                                                <ArrowLeft className="h-5 w-5 text-muted-foreground transition-transform group-hover:translate-x-[-4px] group-hover:text-primary shrink-0" />
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <p className="text-sm text-muted-foreground">{relatedTool.description}</p>
                                        </CardContent>
                                    </Card>
                                </Link>
                            ))}
                        </div>
                    </section>
                )}

                {content.seoDescription && (
                    <article>
                        <h2 className="text-2xl font-headline font-bold text-center mb-6">حول أداة {tool.name}</h2>
                        <div className="p-6 bg-card border rounded-lg space-y-4 text-base leading-relaxed text-card-foreground/90 prose-p:my-4 prose-ul:my-4 prose-ol:my-4" dangerouslySetInnerHTML={{ __html: content.seoDescription }} />
                    </article>
                )}
      
                {content.faq && content.faq.length > 0 && (
                    <section>
                        <h2 className="text-2xl font-headline font-bold text-center mb-6">أسئلة شائعة</h2>
                        <Accordion type="single" collapsible className="w-full">
                            {content.faq.map((item: any, index: number) => (
                                <AccordionItem value={`item-${index}`} key={index}>
                                    <AccordionTrigger className="text-right text-lg">{item.question}</AccordionTrigger>
                                    <AccordionContent className="text-base leading-relaxed">
                                        <p>{item.answer}</p>
                                    </AccordionContent>
                                </AccordionItem>
                            ))}
                        </Accordion>
                    </section>
                )}
            </div>
        </div>
    );
}


    